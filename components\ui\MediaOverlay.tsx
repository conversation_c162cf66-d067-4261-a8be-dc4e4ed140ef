import { MediaData } from '@/types/media';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  Alert,
  Dimensions,
  Platform,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import {
  Gesture,
  GestureDetector,
} from 'react-native-gesture-handler';
import PagerView from 'react-native-pager-view';
import Animated, {
  Extrapolation,
  interpolate,
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import MediaCarouselItem from './MediaCarouselItem';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');
const DISMISS_THRESHOLD = 100;
const HEADER_HEIGHT = 80;

interface MediaOverlayProps {
  media: MediaData[];
  initialIndex: number;
  onClose: () => void;
}

const MediaOverlay: React.FC<MediaOverlayProps> = ({
  media,
  initialIndex,
  onClose,
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [isDownloading, setIsDownloading] = useState(false);
  const [isHeaderVisible, setIsHeaderVisible] = useState(true);
  const pagerRef = useRef<PagerView>(null);
  
  // Animated values for dismiss gesture
  const translateY = useSharedValue(0);
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);
  const headerOpacity = useSharedValue(1);
  const savedTranslateY = useSharedValue(0);

  // Auto-hide header after 3 seconds, reset timer when visibility changes
  useEffect(() => {
    if (isHeaderVisible) {
      const timer = setTimeout(() => {
        setIsHeaderVisible(false);
        headerOpacity.value = withTiming(0, { duration: 300 });
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [isHeaderVisible, headerOpacity]);

  const toggleHeaderVisibility = useCallback(() => {
    const newVisibility = !isHeaderVisible;
    setIsHeaderVisible(newVisibility);
    headerOpacity.value = withTiming(newVisibility ? 1 : 0, { duration: 300 });
  }, [isHeaderVisible, headerOpacity]);

  const handleClose = useCallback(() => {
    onClose();
  }, [onClose]);

  // Tap gesture for header toggle
  const tapGesture = Gesture.Tap()
    .onEnd(() => {
      runOnJS(toggleHeaderVisibility)();
    });

  // Pan gesture for dismiss functionality
  const panGesture = Gesture.Pan()
    .activeOffsetY([-10, 10]) // Only activate for vertical gestures
    .failOffsetX([-30, 30]) // Fail for horizontal gestures to allow PagerView swiping
    .onStart(() => {
      savedTranslateY.value = translateY.value;
    })
    .onUpdate((event) => {
      // Only handle vertical translation
      if (Math.abs(event.translationY) > Math.abs(event.translationX)) {
        translateY.value = savedTranslateY.value + event.translationY;
        
        // Calculate scale and opacity based on translation
        const progress = Math.abs(translateY.value) / DISMISS_THRESHOLD;
        scale.value = interpolate(
          progress,
          [0, 1],
          [1, 0.8],
          Extrapolation.CLAMP
        );
        opacity.value = interpolate(
          progress,
          [0, 1],
          [1, 0.5],
          Extrapolation.CLAMP
        );
      }
    })
    .onEnd(() => {
      const shouldDismiss = Math.abs(translateY.value) > DISMISS_THRESHOLD;
      
      if (shouldDismiss) {
        // Animate to dismissed state
        translateY.value = withTiming(
          translateY.value > 0 ? SCREEN_HEIGHT : -SCREEN_HEIGHT,
          { duration: 200 }
        );
        scale.value = withTiming(0.7, { duration: 200 });
        opacity.value = withTiming(0, { duration: 200 }, (finished) => {
          if (finished) {
            runOnJS(handleClose)();
          }
        });
      } else {
        // Spring back to original position
        translateY.value = withSpring(0);
        scale.value = withSpring(1);
        opacity.value = withSpring(1);
        savedTranslateY.value = 0;
      }
    });

  // Animated styles
  const containerAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateY: translateY.value },
        { scale: scale.value },
      ],
      opacity: opacity.value,
    };
  });

  const headerAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: headerOpacity.value,
    };
  });

  // Download media function
  const downloadMedia = async () => {
    try {
      setIsDownloading(true);
      
      // Request permissions
      const { status } = await MediaLibrary.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission denied', 'Media library access is required to download media.');
        return;
      }

      const currentMedia = media[currentIndex];
      if (!currentMedia) return;

      // Download the file
      const fileUri = FileSystem.documentDirectory + `media_${Date.now()}.${currentMedia.type === 'video' ? 'mp4' : 'jpg'}`;
      const downloadResult = await FileSystem.downloadAsync(currentMedia.url, fileUri);

      // Save to media library
      const asset = await MediaLibrary.createAssetAsync(downloadResult.uri);
      const album = await MediaLibrary.getAlbumAsync('WConnect');
      
      if (album) {
        await MediaLibrary.addAssetsToAlbumAsync([asset], album, false);
      } else {
        await MediaLibrary.createAlbumAsync('WConnect', asset, false);
      }

      Alert.alert('Success', 'Media saved to your gallery successfully!');
    } catch (error) {
      console.error('Download failed:', error);
      Alert.alert('Error', 'Failed to download media. Please try again.');
    } finally {
      setIsDownloading(false);
    }
  };

  const onPageSelected = (event: any) => {
    setCurrentIndex(event.nativeEvent.position);
  };

  // Combine gestures
  const combinedGesture = Gesture.Race(tapGesture, panGesture);

  // Create pager view props
  const pagerViewProps = {
    ref: pagerRef,
    style: styles.pager,
    initialPage: initialIndex,
    onPageSelected: onPageSelected,
  };

  return (
    <View style={styles.container}>
      <StatusBar hidden />
      
      {/* Background blur overlay */}
      <BlurView intensity={0} style={StyleSheet.absoluteFillObject} />
      
      <GestureDetector gesture={combinedGesture}>
        <Animated.View style={[styles.gestureContainer, containerAnimatedStyle]}>
          {/* Header */}
          <Animated.View style={[styles.header, headerAnimatedStyle]}>
            <TouchableOpacity
              style={styles.headerButton}
              onPress={handleClose}
              activeOpacity={0.7}
            >
              <Ionicons name="close" size={28} color="#fff" />
            </TouchableOpacity>
            
            <View style={styles.headerCenter}>
              <Text style={styles.headerTitle}>
                {currentIndex + 1} of {media.length}
              </Text>
            </View>
            
            <TouchableOpacity
              style={styles.headerButton}
              onPress={downloadMedia}
              disabled={isDownloading}
              activeOpacity={0.7}
            >
              <Ionicons 
                name={isDownloading ? "cloud-download-outline" : "download-outline"} 
                size={24} 
                color="#fff" 
              />
            </TouchableOpacity>
          </Animated.View>

          {/* Media carousel */}
          <View style={styles.mediaContainer}>
            <PagerView {...pagerViewProps}>
              {media.map((mediaItem, index) => (
                <View key={index} style={styles.pageContainer}>
                  <MediaCarouselItem
                    media={mediaItem}
                    isActive={index === currentIndex}
                    onSingleTap={toggleHeaderVisibility}
                  />
                </View>
              ))}
            </PagerView>
          </View>
        </Animated.View>
      </GestureDetector>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  gestureContainer: {
    flex: 1,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: HEADER_HEIGHT,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'ios' ? 44 : 20,
    zIndex: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  headerButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  mediaContainer: {
    flex: 1,
  },
  pager: {
    flex: 1,
  },
  pageContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default MediaOverlay;